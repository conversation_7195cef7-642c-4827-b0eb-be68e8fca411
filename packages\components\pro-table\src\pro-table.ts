import { type PaginationProps } from 'element-plus'
import { buildProp, buildProps } from '@neue-plus/utils'
import { ProFormProps } from '../../pro-form'
import type { ExtractPropTypes, PropType } from 'vue'

type RequestParams = {
  skip: number
  top: number
  [key: string]: any
}
type SortOrder = 'ascend' | 'descend'

type Sorter = Record<string, SortOrder>

type Filter = Record<string, (string | number | boolean)[] | null>
type Request = (
  params: RequestParams,
  sort?: Sorter,
  filter?: Filter
) => Promise<{
  data: any[]
  total: number
  success: boolean
}>
export enum ValueTypeEnum {
  Avatar = 'avatar',
  Cascader = 'cascader',
  Checkbox = 'checkbox',
  Code = 'code',
  Color = 'color',
  Date = 'date',
  DateMonth = 'dateMonth',
  DateQuarter = 'dateQuarter',
  DateRange = 'dateRange',
  DateTime = 'dateTime',
  DateTimeRange = 'dateTimeRange',
  DateWeek = 'dateWeek',
  DateYear = 'dateYear',
  Dependency = 'dependency',
  Digit = 'digit',
  Divider = 'divider',
  FormList = 'formList',
  FormSet = 'formSet',
  FromNow = 'fromNow',
  Group = 'group',
  Image = 'image',
  Input = 'input',
  JsonCode = 'jsonCode',
  Money = 'money',
  Password = 'password',
  Percent = 'percent',
  Progress = 'progress',
  Radio = 'radio',
  RadioButton = 'radioButton',
  Rate = 'rate',
  Second = 'second',
  Select = 'select',
  Segmented = 'segmented',
  Switch = 'switch',
  Text = 'text',
  Textarea = 'textarea',
  Time = 'time',
  TimeRange = 'timeRange',
  TreeSelect = 'treeSelect',
}
export type ValueType =
  | 'avatar'
  | 'cascader'
  | 'checkbox'
  | 'code'
  | 'color'
  | 'date'
  | 'dateMonth'
  | 'dateQuarter'
  | 'dateRange'
  | 'dateTime'
  | 'dateTimeRange'
  | 'dateWeek'
  | 'dateYear'
  | 'dependency'
  | 'digit'
  | 'divider'
  | 'formList'
  | 'formSet'
  | 'fromNow'
  | 'group'
  | 'image'
  | 'input'
  | 'jsonCode'
  | 'money'
  | 'password'
  | 'percent'
  | 'progress'
  | 'radio'
  | 'radioButton'
  | 'rate'
  | 'second'
  | 'select'
  | 'segmented'
  | 'switch'
  | 'text'
  | 'textarea'
  | 'time'
  | 'timeRange'
  | 'treeSelect'
/** 单个枚举项的定义 */
export interface ValueEnumItem {
  /** 展示的文本 */
  text: string
  /** 标签颜色状态，参考 ElementPlus / Ant Design 等组件库 */
  status?: 'success' | 'error' | 'processing' | 'default' | 'warning'
  /** 自定义颜色，例如 '#13c2c2' */
  color?: string
  /** 是否禁用该项 */
  disabled?: boolean
}

/** valueEnum 类型：键值对映射（key 可以是字符串或数字） */
export type ValueEnum = Record<string | number, ValueEnumItem>

// ProTable列配置，扩展了筛选功能
export interface ProTableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  align?: 'left' | 'center' | 'right'
  showOverflowTooltip?: boolean
  // 筛选相关
  valueType?: ValueType
  valueEnum?: ValueEnum
  placeholder?: string

  // 在 Table 中不展示此列
  hideInTable?: boolean
  // 在 Form 中不展示此列
  hideInForm?: boolean
  //传递给 Form.Item 的配置
  defaultValue?: number | string | boolean
}
// 分页配置
export interface ProTablePagination extends Partial<PaginationProps> {
  current?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

// 筛选表单数据
export interface ProTableFilters {
  [key: string]: any
}

export type FilterType = 'query' | 'light'
export const proTableProps = buildProps({
  // 表格数据
  data: buildProp({
    type: Array,
    default: () => [],
  }),
  // 列配置
  columns: buildProp({
    type: Array as () => ProTableColumn[],
    required: true,
  }),
  // 搜索表单配置
  searchConfig: buildProp({
    type: [Object, Boolean] as PropType<ProFormProps | false>,
    default: () => ({} as ProFormProps),
  }),
  paginationConfig: buildProp({
    type: [Object, Boolean] as PropType<ProTablePagination | false>,
    default: () => ({} as ProTablePagination),
  }),
  request: buildProp({
    type: Function as PropType<Request>,
    default: undefined,
  }),
  loading: buildProp({
    type: Boolean,
    default: false,
  }),
  bordered: buildProp({
    type: Boolean,
    default: false,
  }),
} as const)

export type ProTableProps = ExtractPropTypes<typeof proTableProps>

// 事件类型 - 使用函数类型定义
export type ProTableEmits = {
  (e: 'filter-change', filters: ProTableFilters): void
  (e: 'page-change', current: number, pageSize: number): void
  (
    e: 'sort-change',
    prop: string,
    order: 'ascending' | 'descending' | null
  ): void
  (e: 'refresh'): void
}
